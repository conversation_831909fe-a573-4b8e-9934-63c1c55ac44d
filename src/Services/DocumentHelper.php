<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\Contract;
use App\Entity\Main\Document;
use App\Entity\Main\DocumentType;
use Doctrine\ORM\EntityManagerInterface;
use Ramsey\Uuid\Uuid;

class DocumentHelper
{
    private ?Document $document = null;

    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    public function createDocument(array $values, DocumentType $documentType, Contract $contract): Document
    {
        $this->document = new Document();
        $this->document->setUuid(uuid: Uuid::uuid4());
        $this->document->setVisible(visible: true);
        $this->document->setActive(active: true);

        return $this->saveDocument(values: $values, documentType: $documentType, contract: $contract);
    }

    public function saveDocument(array $values, DocumentType $documentType, Contract $contract): Document
    {
        $this->document->setWeighingNumber($values['weighingNumber']);

        $this->document->setAmount($values['amount']);

        $this->document->setUnit($values['unit']);

        $this->document->setDocumentType($documentType);

        $this->document->setContract($contract);

        $this->manager->persist($this->document);

        $this->manager->flush();

        return $this->document;
    }
}
