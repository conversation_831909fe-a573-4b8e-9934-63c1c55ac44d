<?php

declare(strict_types=1);

namespace App\Services;

use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

class CaptchaVerifier
{
    public function __construct(
        #[Autowire('%env(CAPTCHA_SECRET)%')]
        private readonly string $hCaptchaSecret,
        #[Autowire('%env(CAPTCHA_VERIFY_URL)%')]
        private readonly string $hCaptchaUrl,
        private readonly RequestStack $requestStack)
    {
    }

    public function verify(string $hCaptchaResponse): bool
    {
        $request = $this->requestStack->getCurrentRequest();

        if (!$request instanceof Request) {
            return false;
        }

        $clientIp = $request->getClientIp();

        $data = [
            'secret' => $this->hCaptchaSecret,
            'response' => $hCaptchaResponse,
            'remoteip' => $clientIp,
        ];

        $ch = curl_init(url: $this->hCaptchaUrl);
        curl_setopt(handle: $ch, option: CURLOPT_POST, value: 1);
        curl_setopt(handle: $ch, option: CURLOPT_POSTFIELDS, value: http_build_query(data: $data));
        curl_setopt(handle: $ch, option: CURLOPT_RETURNTRANSFER, value: true);

        $response = curl_exec(handle: $ch);
        curl_close(handle: $ch);

        $result = json_decode(json: $response, associative: true);

        return $result['success'] ?? false;
    }
}
