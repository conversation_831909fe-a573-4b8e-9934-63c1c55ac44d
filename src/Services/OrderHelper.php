<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Contract;
use App\Entity\Main\ContractArea;
use App\Entity\Main\Order;
use App\Repository\Main\CollectingPlaceRepository;
use App\Repository\Main\ContractRepository;
use Doctrine\ORM\EntityManagerInterface;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;
use Ramsey\Uuid\Uuid;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class OrderHelper
{
    private readonly Client $guzzle;

    /**
     * DefaultController constructor.
     */
    public function __construct(
        private readonly EntityManagerInterface $manager,
        private readonly ParameterBagInterface $parameterBag,
        private readonly Security $security,
    ) {
        $this->guzzle = new Client(config: [
            'base_uri' => $this->parameterBag->get('sap_api_url'),
            'auth' => [
                $this->parameterBag->get('sap_api_user'),
                $this->parameterBag->get('sap_api_password'),
            ],
            'verify' => false,
        ]);
    }

    /**
     * @throws \Exception
     */
    public function createOrder(array $values, ContractArea $area): Order
    {
        $order = new Order();

        return $this->saveOrder(values: $values, order: $order, area: $area);
    }

    /**
     * @throws \Exception
     */
    public function saveOrder(array $values, Order $order, ContractArea $area): Order
    {
        /** @var CollectingPlaceRepository */
        $collectingPlaceRepro = $this->manager->getRepository(CollectingPlace::class);

        /** @var ContractRepository */
        $contractRepro = $this->manager->getRepository(Contract::class);

        /** @var CollectingPlace $collectingPlace */
        $collectingPlace = $collectingPlaceRepro->findOneBy(criteria: ['uuid' => $values['collectingPlace']]);

        /** @var Contract $contract */
        $contract = $contractRepro->getContractByData(contractArea: $area, collectingPlace: $collectingPlace, systemProvider: $values['systemProvider']);

        $order->setContractArea(contractArea: $area);

        $order->setContract(contract: $contract);

        $order->setCollectingPlace(collectingPlace: $collectingPlace);

        $order->setUnloadingPoint(unloadingPoint: $contract->getUnloadingPoint());

        $order->setSystemProvider(systemProvider: $values['systemProvider']);
        $order->setCreatedBy(createdBy: $this->security->getUser()->getUuid());
        $order->setCreatedAt(createdAt: new \DateTime());

        $order->setOrderId(orderId: Uuid::uuid4()->toString());

        if ($values['driverMessage']) {
            $order->setDriverMessage(driverMessage: $values['driverMessage']);
        }

        // if ($values['dispoMessage']) {
        //    $order->setDispoMessage($values['dispoMessage']);
        // }

        $order->setDate(date: new \DateTime(datetime: $values['dateTime'].'T00:00:00'));

        $this->manager->persist($order);

        $this->manager->flush();

        return $order;
    }

    public function send(Order $order): array
    {
        $response = null;
        $transferErrors = [];

        try {
            $response = $this->guzzle->request(
                method: 'POST',
                uri: 'orders',
                options: [
                    'timeout' => '10.0',
                    'headers' => [
                        'X-Requested-With' => 'JSONHttpRequest',
                        'Accept' => 'application/json',
                        'Content-Type' => 'application/json',
                        'Accept-Language' => 'de-DE',
                    ],
                    'body' => json_encode(value: [
                        'id' => $order->getOrderId(),
                        'contractAreaId' => $order->getContractArea()->getContractAreaId(),
                        'contractId' => $order->getContract()->getContractId(),
                        'collectingPlaceId' => $order->getCollectingPlace()->getCollectingPlaceId(),
                        'unloadingPointId' => $order->getUnloadingPoint()->getUnloadingPointId(),
                        'systemProviderId' => $order->getSystemProvider()->getSystemProviderId(),
                        'orderDate' => $order->getDate()->format(format: 'Y-m-d\TH:i:s'),
                        'dispoNumber' => strval(value: $order->getDisposalNumber()),
                        'driverInformation' => str_replace(search: ["\r\n", "\r"], replace: ' ', subject: $order->getDriverMessage()),
                        'dispoInformation' => str_replace(search: ["\r\n", "\r"], replace: ' ', subject: $order->getDispoMessage()),
                    ]),
                    // , 'debug' => true
                ]
            );
        } catch (GuzzleException $e) {
            $order->setTransferStatus(transferStatus: 'E');
            $transferErrors[] = 'Error during transfer of order: '.$order->getDisposalNumber(
            ).' :: '.$e->getMessage();
        }
        if ($response instanceof ResponseInterface) {
            if (201 === $response->getStatusCode()) {
                $order->setTransferStatus(transferStatus: 'S');
            } else {
                $order->setTransferStatus(transferStatus: 'E');
                $transferErrors[] = 'Transfer error for disposal number: '.$order->getDisposalNumber();
            }
        }

        $order->setTransfered(transfered: new \DateTime());
        $this->manager->persist($order);
        $this->manager->flush();

        return $transferErrors;
    }
}
