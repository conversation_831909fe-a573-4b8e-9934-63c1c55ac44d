<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\Order;

class WeekDayIterator implements \Iterator
{
    /**
     * monday.
     */
    public const MON = 'Mon';

    /**
     * tuesday.
     */
    public const TUE = 'Tue';

    /**
     * wednesday.
     */
    public const WED = 'Wed';

    /**
     * thursday.
     */
    public const THU = 'Thu';

    /**
     * friday.
     */
    public const FRI = 'Fri';

    /**
     * saturday.
     */
    public const SAT = 'Sat';

    /**
     * current position of array.
     */
    private int $position = 0;

    /**
     * Template for each entry/row.
     *
     * @var null[]
     */
    private array $template = [
        WeekDayIterator::MON => null,
        WeekDayIterator::TUE => null,
        WeekDayIterator::WED => null,
        WeekDayIterator::THU => null,
        WeekDayIterator::FRI => null,
        WeekDayIterator::SAT => null,
    ];

    /**
     * Iterated.
     */
    private array $array = [];

    private int $countMon = 0;

    private int $countTue = 0;

    private int $countWed = 0;

    private int $countThu = 0;

    private int $countFri = 0;

    private int $countSat = 0;

    /**
     * @param Order[] $orderList
     */
    public function addOrderList(array $orderList): void
    {
        foreach ($orderList as $order) {
            $day = $order->getDate()->format(format: 'D');
            switch ($day) {
                case WeekDayIterator::MON:
                    $this->addOrderToDay(count: $this->countMon, day: WeekDayIterator::MON, order: $order);
                    ++$this->countMon;
                    break;
                case WeekDayIterator::TUE:
                    $this->addOrderToDay(count: $this->countTue, day: WeekDayIterator::TUE, order: $order);
                    ++$this->countTue;
                    break;
                case WeekDayIterator::WED:
                    $this->addOrderToDay(count: $this->countWed, day: WeekDayIterator::WED, order: $order);
                    ++$this->countWed;
                    break;
                case WeekDayIterator::THU:
                    $this->addOrderToDay(count: $this->countThu, day: WeekDayIterator::THU, order: $order);
                    ++$this->countThu;
                    break;
                case WeekDayIterator::FRI:
                    $this->addOrderToDay(count: $this->countFri, day: WeekDayIterator::FRI, order: $order);
                    ++$this->countFri;
                    break;
                case WeekDayIterator::SAT:
                    $this->addOrderToDay(count: $this->countSat, day: WeekDayIterator::SAT, order: $order);
                    ++$this->countSat;
                    break;
            }
        }
    }

    private function addOrderToDay(int $count, string $day, Order $order): void
    {
        if (!array_key_exists(key: $count, array: $this->array)) {
            $this->array[$count] = $this->template;
        }
        $this->array[$count][$day] = $order;
    }

    public function maxCount(): int
    {
        return count(value: $this->array);
    }

    public function lastElementMon(): int
    {
        return $this->countMon;
    }

    public function lastElementTue(): int
    {
        return $this->countTue;
    }

    public function lastElementWed(): int
    {
        return $this->countWed;
    }

    public function lastElementThu(): int
    {
        return $this->countThu;
    }

    public function lastElementFri(): int
    {
        return $this->countFri;
    }

    public function lastElementSat(): int
    {
        return $this->countSat;
    }

    public function isMonInLastRow(): bool
    {
        return $this->maxCount() === $this->lastElementMon();
    }

    public function isTueInLastRow(): bool
    {
        return $this->maxCount() === $this->lastElementTue();
    }

    public function isWedInLastRow(): bool
    {
        return $this->maxCount() === $this->lastElementWed();
    }

    public function isThuInLastRow(): bool
    {
        return $this->maxCount() === $this->lastElementThu();
    }

    public function isFriInLastRow(): bool
    {
        return $this->maxCount() === $this->lastElementFri();
    }

    public function isSatInLastRow(): bool
    {
        return $this->maxCount() === $this->lastElementSat();
    }

    public function rewind(): void
    {
        $this->position = 0;
    }

    public function current(): mixed
    {
        return $this->array[$this->position];
    }

    public function key(): mixed
    {
        return $this->position;
    }

    public function next(): void
    {
        ++$this->position;
    }

    public function valid(): bool
    {
        return isset($this->array[$this->position]);
    }
}
