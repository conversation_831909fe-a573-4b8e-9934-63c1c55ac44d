<?php

declare(strict_types=1);

namespace App\Services;

class FileExtensionHelper
{
    private array $extensionMapping = [
        'text/plain' => '.txt',
        'image/png' => '.png',
        'image/jpg' => '.jpg',
        'image/gif' => '.gif',
        'application/pdf' => '.pdf',
    ];

    public function getFileExtensionByMimeType(string $mimeType): string
    {
        if (array_key_exists(key: $mimeType, array: $this->extensionMapping)) {
            return $this->extensionMapping[$mimeType];
        } else {
            return '';
        }
    }
}
