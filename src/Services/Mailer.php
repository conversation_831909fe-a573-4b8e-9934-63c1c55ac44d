<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\Order;
use App\Entity\Main\User;
use Psr\Log\LoggerInterface;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * Class Mailer.
 */
class Mailer
{
    /**
     * Mailer constructor.
     *
     * @param string $emailManager
     */
    public function __construct(
        private readonly MailerInterface $mailer,
        private readonly LoggerInterface $logger,
        private readonly HashcodeHelper $hashcodeHelper,
        private readonly Security $security,
        private readonly string $emailFrom,
        private readonly string $emailFromName,
        private readonly string $emailReplyTo,
        private readonly string $mailBaselink,
        private readonly array $emailManager,
        private readonly array $emailOrder,
        private readonly array $emailStorno,
        private readonly array $emailQuestion,
    ) {
    }

    public function sendPasswordReset(User $user, $newUser = false): void
    {
        $template = 'emails/passwordreset.html.twig';
        $subject = 'PreZero Mengenmeldung - Passwort zurücksetzen';

        if ($user->getLocked()) {
            return;
        }

        if ($newUser) {
            $template = 'emails/password-newuser.html.twig';
            $subject = 'PreZero Mengenmeldung - Neuer Zugang';
        }

        try {
            $this->hashcodeHelper->setUserPasswordResetHash(user: $user);
        } catch (\Exception $e) {
            $this->logger->error(
                'Mail sending Password Reset not working:: Calculating Reset Hash not working: '.$e->getMessage(
                ).' '.$e->getTraceAsString()
            );
        }

        $data = [
            'hashCode' => $user->getPasswordResetHash(),
            'mailBaseLink' => $this->mailBaselink,
        ];

        $this->sendMail(template: $template, data: $data, subject: $subject, emailsTo: $user->getEmail());
    }

    public function sendContactMessage(array $data, string $userMailAddress): void
    {
        $template = 'emails/contact_message.html.twig';
        $emailTo = $this->emailQuestion;

        switch ($data['request_types']) {
            case 'order_question':
                $title = 'Anfrage zum Auftrag';
                $emailTo = $this->emailOrder;
                break;
            case 'cancel_request':
                $title = 'Stornierung';
                $emailTo = $this->emailStorno;
                break;
            case 'general_request':
                $title = 'Allgemeine Anfrage';
                $emailTo = $this->emailQuestion;
                break;
            default:
                $title = 'Nachricht vom Kontaktformular';
                break;
        }

        if (!$this->security->getUser() instanceof UserInterface) {
            $title .= ' (öffentlich)';
        }

        $subject = 'PreZero Mengenmeldung - '.$title;

        $data = [
            'title' => $title,
            'collecting_place' => $data['collecting_place'],
            'contract_area' => $data['contract_area'] ?? null,
            'company' => $data['company'] ?? null,
            'disponumber' => $data['disponumber'] ?? null,
            'message' => $data['message'],
            'mailBaseLink' => $this->mailBaselink,
            'userMailAddress' => $userMailAddress,
        ];

        $this->sendMail(template: $template, data: $data, subject: $subject, emailsTo: $emailTo);
    }

    public function sendOrderList(User $user, array $orderList, $dateFrom, $dateTo, $weekStart): void
    {
        $template = 'emails/orderlist.html.twig';
        $subject = 'PreZero Mengenmeldung - Anfragenübersicht';

        if (!is_null(value: $dateFrom) && !is_null(value: $dateTo)) {
            $kw = 'KW'.$dateFrom->format('W').'-'.$dateFrom->format('Y');
            $subject = $subject.' ('.$kw.')';
        }

        $data = [
            'orders' => $orderList,
            'weekStart' => $weekStart,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'mailBaseLink' => $this->mailBaselink,
            'numOfOrders' => (count(value: $orderList) > 1),
        ];

        $this->sendMail(template: $template, data: $data, subject: $subject, emailsTo: $user->getEmail());
    }

    public function sendErrorMessages(array $errors): void
    {
        $template = 'emails/errors.html.twig';
        $subject = 'PreZero Mengenmeldung - fehlerhafte Übertragung';

        $data = [
            'errors' => $errors,
            'mailBaseLink' => $this->mailBaselink,
        ];

        $this->sendMail(template: $template, data: $data, subject: $subject, emailsTo: $this->emailManager);
    }

    public function sendCancelOrder(User $user, Order $orderToCancel, bool $timeLimitToCancel): void
    {
        $this->sendCancelOrderToUser(orderToCancel: $orderToCancel, timeLimitToCancel: $timeLimitToCancel, user: $user);
        $this->sendCancelOrderToCompany(orderToCancel: $orderToCancel, user: $user);
    }

    private function sendCancelOrderToUser(Order $orderToCancel, bool $timeLimitToCancel, User $user): void
    {
        $template = 'emails/cancelorder.html.twig';
        $subject = 'PreZero Mengenmeldung - Stornierung';

        $data = [
            'order' => $orderToCancel,
            'timeLimit' => $timeLimitToCancel,
            'mailBaseLink' => $this->mailBaselink,
        ];

        $this->sendMail(template: $template, data: $data, subject: $subject, emailsTo: $user->getEmail());
    }

    private function sendCancelOrderToCompany(Order $orderToCancel, User $user): void
    {
        $template = 'emails/contact_message.html.twig';
        $subject = 'PreZero Mengenmeldung - Stornierung';

        $data = [
            'title' => 'Stornierung',
            'collecting_place' => $orderToCancel->getCollectingPlace(),
            'contract_area' => $orderToCancel->getContractArea(),
            'company' => null,
            'disponumber' => $orderToCancel->getDisposalNumber(),
            'message' => null,
            'mailBaseLink' => $this->mailBaselink,
            'userMailAddress' => $user->getEmail(),
        ];

        $this->sendMail(template: $template, data: $data, subject: $subject, emailsTo: $this->emailStorno);
    }

    private function sendMail(string $template, array $data, string $subject, string|array|null $emailsTo): void
    {
        $message = new TemplatedEmail()
            ->subject(subject: $subject)
            ->from(new Address(address: $this->emailFrom, name: $this->emailFromName))
            ->replyTo($this->emailReplyTo)
            ->htmlTemplate(template: $template)
            ->context(context: $data);

        if (!is_array(value: $emailsTo)) {
            $message->addTo(new Address(address: $emailsTo));
        } else {
            foreach ($emailsTo as $emailTo) {
                $message->addTo(new Address(address: $emailTo));
            }
        }

        try {
            $this->mailer->send($message);
        } catch (\Exception $e) {
            $this->logger->error('Mail sending not working: '.$e->getMessage().' '.$e->getTraceAsString());
        }
    }
}
