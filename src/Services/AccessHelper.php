<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\ContractArea;
use App\Entity\Main\User;
use App\Entity\Main\UserAccess;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class AccessHelper
{
    public function __construct(protected LoggerInterface $logger, protected EntityManagerInterface $manager, private readonly ContractAreaValidityHelper $cavHelper)
    {
    }

    public function getContractAreaList(User $user, ?CollectingPlace $collectingPlace = null): array
    {
        $criteria['user'] = $user;

        if ($collectingPlace instanceof CollectingPlace) {
            $criteria['collectingPlace'] = $collectingPlace;
        }

        $userAccessList = $this->manager->getRepository(UserAccess::class)->findBy(criteria: $criteria);

        $contractAreaList = [];
        /** @var UserAccess $userAccess */
        foreach ($userAccessList as $userAccess) {
            $validBool = $this->cavHelper->hasValidityForDate(contractArea: $userAccess->getContractArea(), date: new \DateTime(datetime: 'NOW'));
            if ($validBool) {
                $contractAreaList[] = $userAccess->getContractArea();
            }
        }

        return array_unique(array: $contractAreaList);
    }

    public function getCollectingPlaceList(User $user, ?ContractArea $contractArea = null, $locked = false): array
    {
        $criteria['user'] = $user;

        if ($contractArea instanceof ContractArea) {
            $criteria['contractArea'] = $contractArea;
        }

        $userAccessList = $this->manager->getRepository(UserAccess::class)->findBy(criteria: $criteria);

        $collectingPlaces = [];
        /** @var UserAccess $userAccess */
        foreach ($userAccessList as $userAccess) {
            if ($locked || !$userAccess->getCollectingPlace()->getLocked()) {
                $collectingPlaces[] = $userAccess->getCollectingPlace();
            }
        }

        return array_unique(array: $collectingPlaces);
    }

    /**
     * @return mixed[]
     */
    public function getUserList(?ContractArea $contractArea = null, ?CollectingPlace $collectingPlace = null): array
    {
        if (is_null(value: $contractArea) && is_null(value: $collectingPlace)) {
            return [];
        }

        $criteria = [];

        if ($collectingPlace instanceof CollectingPlace) {
            $criteria['collectingPlace'] = $collectingPlace;
        }

        if ($contractArea instanceof ContractArea) {
            $criteria['contractArea'] = $contractArea;
        }

        $userAccessList = $this->manager->getRepository(UserAccess::class)->findBy(criteria: $criteria);

        $users = [];
        /** @var UserAccess $userAccess */
        foreach ($userAccessList as $userAccess) {
            if (!in_array(needle: $userAccess->getUser(), haystack: $users)) {
                $users[] = $userAccess->getUser();
            }
        }

        return $users;
    }

    public function checkUserAccessCombination(User $user, ContractArea $contractArea, CollectingPlace $collectingPlace): bool
    {
        if (is_null(value: $collectingPlace)) {
            return false;
        }

        return $this->checkUserAccess(user: $user, contractArea: $contractArea, collectingPlace: $collectingPlace);
    }

    public function checkUserAccessForCollectingPlace(User $user, CollectingPlace $collectingPlace): bool
    {
        if (is_null(value: $collectingPlace)) {
            return false;
        }

        return $this->checkUserAccess(user: $user, collectingPlace: $collectingPlace);
    }

    public function checkUserAccessForContractArea(User $user, ContractArea $contractArea): bool
    {
        if (is_null(value: $contractArea)) {
            return false;
        }

        return $this->checkUserAccess(user: $user, contractArea: $contractArea);
    }

    private function checkUserAccess(User $user, ?ContractArea $contractArea = null, ?CollectingPlace $collectingPlace = null): bool
    {
        if (is_null(value: $user)) {
            return false;
        }

        if ($contractArea && $collectingPlace && !$this->checkContractAreaCollectingPlace(contractArea: $contractArea, collectingPlace: $collectingPlace)) {
            return false;
        }

        $criteria['user'] = $user;
        if (!is_null(value: $contractArea)) {
            $criteria['contractArea'] = $contractArea;
        }
        if (!is_null(value: $collectingPlace)) {
            $criteria['collectingPlace'] = $collectingPlace;
        }

        $userAccess = $this->manager->getRepository(UserAccess::class)->findOneBy(criteria: $criteria);

        return !is_null(value: $userAccess) && $userAccess;
    }

    private function checkContractAreaCollectingPlace(ContractArea $contractArea, CollectingPlace $collectingPlace): bool
    {
        foreach ($contractArea->getContracts() as $contract) {
            if ($contract->getCollectingPlace() === $collectingPlace) {
                return true;
            }
        }

        return false;
    }
}
