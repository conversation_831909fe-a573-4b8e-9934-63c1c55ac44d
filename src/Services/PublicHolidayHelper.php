<?php

declare(strict_types=1);

namespace App\Services;

use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;

class PublicHolidayHelper
{
    public function __construct(
        #[AutowireIterator('app.holiday_resolver')] private readonly iterable $resolvers,
    ) {
    }

    public function findAllPublicHolidayInBetween(string $countryCode, \DateTime $startDate, \DateTime $endDate, array $states): array
    {
        /**
         * @var HolidayResolverInterface $resolver
         */
        foreach ($this->resolvers as $resolver) {
            if ($resolver->supports($countryCode)) {
                $holidays = [];
                $period = new \DatePeriod(
                    $startDate,
                    new \DateInterval(duration: 'P1D'),
                    (clone $endDate)->modify(modifier: '+1 day')
                );
                foreach ($period as $date) {
                    $result = $resolver->resolveHoliday($date->format(format: 'Y-m-d'), $states);
                    if ($result && !empty($result['boolean'])) {
                        $holidays[] = ['date' => $date, 'name' => $result['name']];
                    }
                }

                return $holidays;
            }
        }
        throw new \InvalidArgumentException(message: "No holiday resolver found for country '$countryCode'");
    }

    public function checkHolidayDay(string $countryCode, string $date, array $states): array
    {
        foreach ($this->resolvers as $resolver) {
            if ($resolver->supports($countryCode)) {
                return $resolver->resolveHoliday($date, $states) ?: [];
            }
        }

        return [];
    }
}
