<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\StateRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class State (Bundesland).
 */
#[ORM\Table(name: 'state')]
#[ORM\Entity(repositoryClass: StateRepository::class)]
class State implements HasUuid, HasCreateModifyStamps, \Stringable
{
    use CommonTrait;
    public $publicHolidays;

    #[ORM\Column(name: 'name', type: Types::STRING, length: 255, nullable: false)]
    protected string $name; // Bundesland
    #[ORM\Column(name: 'short_name', type: Types::STRING, length: 255, nullable: false)]
    protected string $shortName;

    /**
     * @var Collection<int, ContractArea>
     */
    #[ORM\OneToMany(targetEntity: 'ContractArea', mappedBy: 'state')]
    protected Collection $contractAreas;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): State
    {
        $this->name = $name;

        return $this;
    }

    public function getShortName(): string
    {
        return $this->shortName;
    }

    public function setShortName(string $shortName): State
    {
        $this->shortName = $shortName;

        return $this;
    }

    /** @psalm-suppress UndefinedMethod */
    public function addContractArea(ContractArea $contractArea): void
    {
        if ($this->contractAreas->contains($contractArea)) {
            return;
        }

        $this->contractAreas->add($contractArea);
        $contractArea->addState($this);
    }

    /**
     * @psalm-suppress UndefinedMethod
     * @psalm-suppress UndefinedThisPropertyFetch
     */
    public function removeContractArea(ContractArea $contractArea): void
    {
        if (!$this->contractAreas->contains($contractArea)) {
            return;
        }

        $this->publicHolidays->removeElement($contractArea);
        $contractArea->removeState($this);
    }

    /**
     * @return Collection<int, ContractArea>
     */
    public function getContractAreas(): Collection
    {
        return $this->contractAreas;
    }

    /**
     * @param Collection<int, ContractArea> $contractAreas
     */
    public function setContractAreas(Collection $contractAreas): State
    {
        $this->contractAreas = $contractAreas;

        return $this;
    }

    public function __toString(): string
    {
        return $this->getName();
    }

    public function __construct()
    {
        $this->contractAreas = new ArrayCollection();
    }
}
