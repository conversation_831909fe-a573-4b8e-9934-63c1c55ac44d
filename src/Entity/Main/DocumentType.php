<?php

declare(strict_types=1);

namespace App\Entity\Main;

// use App\Repository\Main\DocumentTypeRepository;
// use Doctrine\Common\Collections\ArrayCollection;
use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\DocumentTypeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

// @ORM\Entity(repositoryClass=DocumentTypeRepository::class)

#[ORM\Entity(repositoryClass: DocumentTypeRepository::class)]
class DocumentType implements HasUuid, HasCreateModifyStamps
{
    use CommonTrait;

    #[ORM\Column(type: Types::STRING, length: 255)]
    private ?string $name = null;

    /**
     * @var Collection<int, Document>
     */
    #[ORM\OneToMany(targetEntity: Document::class, mappedBy: 'documentType')]
    private Collection $documents;

    public function __construct()
    {
        $this->documents = new ArrayCollection();
        // $this->documents = new ArrayCollection();
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection<int, Document>
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(Document $document): self
    {
        if (!$this->documents->contains($document)) {
            $this->documents->add($document);
            $document->setDocumentType(documentType: $this);
        }

        return $this;
    }

    public function removeDocument(Document $document): self
    {
        // set the owning side to null (unless already changed)
        if ($this->documents->removeElement($document) && $document->getDocumentType() === $this) {
            $document->setDocumentType();
        }

        return $this;
    }
}
