<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\ContractAreaRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class ContractArea (Vertragsgebiet).
 */
#[ORM\Table(name: 'contract_area')]
#[ORM\Entity(repositoryClass: ContractAreaRepository::class)]
class ContractArea implements HasUuid, HasCreateModifyStamps, \Stringable
{
    use CommonTrait;

    #[ORM\Column(name: 'contract_area_id', type: Types::STRING, length: 255, nullable: false)]
    protected string $contractAreaId;

    #[ORM\Column(name: 'name', type: Types::STRING, length: 255, nullable: false)]
    protected string $name;

    /**
     * @var \DateTime
     */
    #[ORM\Column(name: 'valid_from', type: Types::DATETIME_MUTABLE, nullable: false)]
    protected \DateTimeInterface $validFrom;

    /**
     * @var \DateTime
     */
    #[ORM\Column(name: 'valid_to', type: Types::DATETIME_MUTABLE, nullable: false)]
    protected \DateTimeInterface $validTo;

    /**
     * @var State
     */
    #[ORM\ManyToOne(targetEntity: 'State', inversedBy: 'contractAreas')]
    #[ORM\JoinColumn(name: 'state', referencedColumnName: 'id', nullable: false)]
    protected $state;

    /**
     * @var Collection<int, ContractAreaValidity>
     */
    #[ORM\OneToMany(targetEntity: ContractAreaValidity::class, mappedBy: 'contractArea')]
    private Collection $contractAreaValidities;

    /**
     * @var Collection<int, Contract>
     */
    #[ORM\OneToMany(targetEntity: Contract::class, mappedBy: 'contractArea')]
    private Collection $contracts;

    /**
     * @var Collection<int, Order>
     */
    #[ORM\OneToMany(targetEntity: Order::class, mappedBy: 'contractArea')]
    private Collection $orders;

    #[ORM\OneToMany(targetEntity: 'UserAccess', mappedBy: 'contractArea')]
    private Collection $userAccessList;

    public function __construct()
    {
        $this->contractAreaValidities = new ArrayCollection();
        $this->contracts = new ArrayCollection();
        $this->orders = new ArrayCollection();
        $this->userAccessList = new ArrayCollection();
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): ContractArea
    {
        $this->name = $name;

        return $this;
    }

    public function getState(): State
    {
        return $this->state;
    }

    /**
     * @param State
     */
    public function setState($state): ContractArea
    {
        $this->state = $state;

        return $this;
    }

    public function getContractAreaId(): string
    {
        return $this->contractAreaId;
    }

    public function setContractAreaId(string $contractAreaId): ContractArea
    {
        $this->contractAreaId = $contractAreaId;

        return $this;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function setValidFrom(\DateTime $validFrom): ContractArea
    {
        $this->validFrom = $validFrom;

        return $this;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }

    public function setValidTo(\DateTime $validTo): ContractArea
    {
        $this->validTo = $validTo;

        return $this;
    }

    /**
     * @return Collection<int, ContractAreaValidity>
     */
    public function getContractAreaValidities(): Collection
    {
        return $this->contractAreaValidities;
    }

    public function addContractAreaValidity(ContractAreaValidity $contractAreaValidity): self
    {
        if (!$this->contractAreaValidities->contains(element: $contractAreaValidity)) {
            $this->contractAreaValidities->add(element: $contractAreaValidity);
            $contractAreaValidity->setContractArea(contractArea: $this);
        }

        return $this;
    }

    public function removeContractAreaValidity(ContractAreaValidity $contractAreaValidity): self
    {
        if ($this->contractAreaValidities->contains(element: $contractAreaValidity)) {
            $this->contractAreaValidities->removeElement(element: $contractAreaValidity);
            // set the owning side to null (unless already changed)
            if ($contractAreaValidity->getContractArea() === $this) {
                $contractAreaValidity->setContractArea();
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Contract>
     */
    public function getContracts(): Collection
    {
        return $this->contracts;
    }

    public function addContract(Contract $contract): self
    {
        if (!$this->contracts->contains(element: $contract)) {
            $this->contracts->add(element: $contract);
            $contract->setContractArea(contractArea: $this);
        }

        return $this;
    }

    public function removeContract(Contract $contract): self
    {
        if ($this->contracts->contains(element: $contract)) {
            $this->contracts->removeElement(element: $contract);
            // set the owning side to null (unless already changed)
            if ($contract->getContractArea() === $this) {
                $contract->setContractArea();
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Order>
     */
    public function getOrders(): Collection
    {
        return $this->orders;
    }

    public function addOrder(Order $order): self
    {
        if (!$this->orders->contains(element: $order)) {
            $this->orders->add(element: $order);
            $order->setContractArea(contractArea: $this);
        }

        return $this;
    }

    public function removeOrder(Order $order): self
    {
        if ($this->orders->contains(element: $order)) {
            $this->orders->removeElement(element: $order);
            // set the owning side to null (unless already changed)
            if ($order->getContractArea() === $this) {
                $order->setContractArea();
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, UserAccess>
     */
    public function getUserAccessList(): Collection
    {
        return $this->userAccessList;
    }

    public function addUserAccess(UserAccess $userAccess): self
    {
        if (!$this->userAccessList->contains(element: $userAccess)) {
            $this->userAccessList->add(element: $userAccess);
        }

        return $this;
    }

    public function removeUserAccess(UserAccess $userAccess): self
    {
        $this->userAccessList->removeElement(element: $userAccess);

        return $this;
    }

    public function __toString(): string
    {
        return strval(value: $this->name);
    }
}
