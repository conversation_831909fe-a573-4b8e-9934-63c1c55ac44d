<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\AddressTrait;
use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasLocked;
use App\Entity\Common\HasUuid;
use App\Entity\Common\LockedTrait;
use App\Repository\Main\CollectingPlaceRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class CollectingPlace (Umschlag).
 */
#[ORM\Table(name: 'collecting_place')]
#[ORM\Entity(repositoryClass: CollectingPlaceRepository::class)]
class CollectingPlace implements HasUuid, HasCreateModifyStamps, HasLocked, \Stringable
{
    use CommonTrait;
    use AddressTrait;
    use LockedTrait;

    #[ORM\Column(name: 'collecting_place_id', type: Types::STRING, length: 255, nullable: false)]
    protected string $collectingPlaceId;

    #[ORM\Column(name: 'dsd_id', type: Types::STRING, length: 255, nullable: false)]
    protected string $dsdId;

    #[ORM\Column(name: 'name_1', type: Types::STRING, length: 255, nullable: false)]
    protected string $name1;

    #[ORM\Column(name: 'name_2', type: Types::STRING, length: 255, nullable: true)]
    protected ?string $name2 = null;

    /**
     * @var State
     */
    #[ORM\ManyToOne(targetEntity: 'State', inversedBy: 'contractAreas')]
    #[ORM\JoinColumn(name: 'state', referencedColumnName: 'id', nullable: false)]
    protected $state;

    /**
     * @var Collection<int, Contract>
     */
    #[ORM\OneToMany(targetEntity: Contract::class, mappedBy: 'collectingPlace')]
    private Collection $contracts;

    #[ORM\OneToMany(targetEntity: 'UserAccess', mappedBy: 'collectingPlace')]
    private Collection $userAccessList;

    public function __construct()
    {
        $this->contracts = new ArrayCollection();
        $this->userAccessList = new ArrayCollection();
    }

    public function getCollectingPlaceId(): string
    {
        return $this->collectingPlaceId;
    }

    public function setCollectingPlaceId(string $collectingPlaceId): CollectingPlace
    {
        $this->collectingPlaceId = $collectingPlaceId;

        return $this;
    }

    public function getDsdId(): string
    {
        return $this->dsdId;
    }

    public function setDsdId(string $dsdId): CollectingPlace
    {
        $this->dsdId = $dsdId;

        return $this;
    }

    public function getName1(): string
    {
        return $this->name1;
    }

    public function setName1(string $name1): CollectingPlace
    {
        $this->name1 = $name1;

        return $this;
    }

    public function getName2(): ?string
    {
        return $this->name2;
    }

    public function setName2(?string $name2): CollectingPlace
    {
        $this->name2 = $name2;

        return $this;
    }

    public function getState(): State
    {
        return $this->state;
    }

    /**
     * @param State
     */
    public function setState($state): CollectingPlace
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @return Collection<int, Contract>
     */
    public function getContracts(): Collection
    {
        return $this->contracts;
    }

    public function addContract(Contract $contract): self
    {
        if (!$this->contracts->contains(element: $contract)) {
            $this->contracts->add(element: $contract);
            $contract->setCollectingPlace(collectingPlace: $this);
        }

        return $this;
    }

    public function removeContract(Contract $contract): self
    {
        if ($this->contracts->contains(element: $contract)) {
            $this->contracts->removeElement(element: $contract);
            // set the owning side to null (unless already changed)
            if ($contract->getCollectingPlace() === $this) {
                $contract->setCollectingPlace();
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, UserAccess>
     */
    public function getUserAccessList(): Collection
    {
        return $this->userAccessList;
    }

    public function addUserAccess(UserAccess $userAccess): self
    {
        if (!$this->userAccessList->contains(element: $userAccess)) {
            $this->userAccessList->add(element: $userAccess);
        }

        return $this;
    }

    public function removeUserAccess(UserAccess $userAccess): self
    {
        $this->userAccessList->removeElement(element: $userAccess);

        return $this;
    }

    public function __toString(): string
    {
        return $this->dsdId.' - '.$this->name1.' ('.$this->city.')';
    }
}
