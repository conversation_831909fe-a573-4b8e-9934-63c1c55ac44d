<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\Contract;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class ContractFixtures extends Fixture implements FixtureGroupInterface, DependentFixtureInterface
{
    public const CONTRACT_REFERENCE = 'contract';
    public const CONTRACT_REFERENCE_2 = 'contract2';

    public function load(ObjectManager $manager): void
    {
        $contract = new Contract();
        $contract->setContractId(contractId: '123123');
        $contract->setContractNumber(contractNumber: '123123');
        $contract->setValidFrom(validFrom: new \DateTime(datetime: '2019-01-01'));
        $contract->setValidTo(validTo: new \DateTime(datetime: '9999-12-31'));
        $contract->setCollectingPlace(collectingPlace: $this->getReference(name: CollectingPlaceFixtures::COLLECTINGPLACE_REFERENCE));
        $contract->setUnloadingPoint(unloadingPoint: $this->getReference(name: UnloadingPointFixtures::UNLOADINGPOINT_REFERENCE));
        $contract->setContractArea(contractArea: $this->getReference(name: ContractAreaFixtures::CONTRACT_AREA_REFERENCE));
        $manager->persist($contract);
        $manager->flush();

        $contract_2 = new Contract();
        $contract_2->setContractId(contractId: '78787878');
        $contract_2->setContractNumber(contractNumber: '78787878');
        $contract_2->setValidFrom(validFrom: new \DateTime(datetime: '2019-01-01'));
        $contract_2->setValidTo(validTo: new \DateTime(datetime: '9999-12-31'));
        $contract_2->setCollectingPlace(collectingPlace: $this->getReference(name: CollectingPlaceFixtures::COLLECTINGPLACE_REFERENCE_2));
        $contract_2->setUnloadingPoint(unloadingPoint: $this->getReference(name: UnloadingPointFixtures::UNLOADINGPOINT_REFERENCE_2));
        $contract_2->setContractArea(contractArea: $this->getReference(name: ContractAreaFixtures::CONTRACT_AREA_REFERENCE_2));
        $manager->persist($contract_2);
        $manager->flush();

        // other fixtures can get this object using the ContractFixtures::CONTRACT_REFERENCE constant
        $this->addReference(name: self::CONTRACT_REFERENCE, object: $contract);

        // other fixtures can get this object using the ContractFixtures::CONTRACT_REFERENCE_2 constant
        $this->addReference(name: self::CONTRACT_REFERENCE_2, object: $contract_2);
    }

    public function getDependencies(): array
    {
        return [
            ContractAreaFixtures::class,
            CollectingPlaceFixtures::class,
            UnloadingPointFixtures::class,
        ];
    }

    public static function getGroups(): array
    {
        return ['contract', 'area', 'collectRegistration'];
    }
}
