<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Files\DocumentData;
use App\Entity\Main\Contract;
use App\Entity\Main\Document;
use App\Entity\Main\DocumentType;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\Persistence\ObjectManager;
use Ramsey\Uuid\Uuid;

class DocumentFixtures extends Fixture implements FixtureGroupInterface, DependentFixtureInterface
{
    public const DOCUMENT_REFERENCE = 'document';

    /**
     * DocumentFixtures constructor.
     */
    public function __construct(private readonly ManagerRegistry $managerRegistry)
    {
    }

    public function load(ObjectManager $manager): void
    {
        $fileManager = $this->managerRegistry->getManager('files');

        $fileDBObj = new DocumentData();

        $fp = fopen(filename: './src/DataFixtures/files/example.pdf', mode: 'rb');

        $fileDBObj->setFile(file: stream_get_contents(stream: $fp));

        $fileUuid = Uuid::uuid1();

        $fileDBObj->setUuid(uuid: $fileUuid);

        $fileDBObj->setMimeType(mimeType: 'application/pdf');

        fclose(stream: $fp);

        $fileManager->persist($fileDBObj);

        $fileManager->flush();

        $document = new Document();

        $document->setUuid(uuid: $fileUuid);

        $document->setVisible(visible: true);

        $document->setActive(active: true);

        $document->setAmount(amount: 2323.5);

        $document->setNumber(number: '000001');

        $document->setDate(date: new \DateTime());

        $document->setUnit(unit: 'kg');

        /** @var Contract $contract */
        $contract = $this->getReference(name: ContractFixtures::CONTRACT_REFERENCE);

        $document->setContract(contract: $contract);

        /** @var DocumentType $documentType */
        $documentType = $this->getReference(name: DocumentTypeFixtures::DOCUMENT_TYPE_REFERENCE);

        $document->setDocumentType(documentType: $documentType);

        $manager->persist($document);

        $manager->flush();

        $this->setReference(name: self::DOCUMENT_REFERENCE, object: $document);

        // TEST
    }

    public static function getGroups(): array
    {
        return ['document'];
    }

    public function getDependencies(): array
    {
        return [DocumentTypeFixtures::class, ContractFixtures::class];
    }
}
