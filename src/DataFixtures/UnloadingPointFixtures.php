<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\State;
use App\Entity\Main\UnloadingPoint;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

/**
 * Class UserFixtures.
 */
class UnloadingPointFixtures extends Fixture implements FixtureGroupInterface, DependentFixtureInterface
{
    private ?ObjectManager $manager = null;

    public const UNLOADINGPOINT_REFERENCE = 'UnloadingPoint';

    public const UNLOADINGPOINT_REFERENCE_2 = 'UnloadingPoint2';

    public function load(ObjectManager $manager): void
    {
        $this->manager = $manager;

        $unloadingPoint = $this->createUnloadingPoint(sapId: '22663', name: 'PreZero Recycling Deutschland', street: 'An der Pforte', housnmb: '2', plc: '32457', city: 'Porta Westfalica', state: 'NW', district: 'NW', country: 'DE');
        $unloadingPoint_2 = $this->createUnloadingPoint(sapId: '7220', name: 'PreZero Energy GmbH', street: 'Köthensche Str.', housnmb: '3', plc: '06406', city: 'Bernburg', state: 'SA', district: 'SA', country: 'DE');

        $this->addReference(name: self::UNLOADINGPOINT_REFERENCE, object: $unloadingPoint);

        $this->addReference(name: self::UNLOADINGPOINT_REFERENCE_2, object: $unloadingPoint_2);
    }

    public function createUnloadingPoint(string $sapId, string $name, string $street, string $housnmb, string $plc, string $city, string $state, ?string $district = null, ?string $country = null): UnloadingPoint
    {
        $unloadingPoint = new UnloadingPoint();

        $unloadingPoint->setUnloadingPointId(unloadingPointId: $sapId);
        $unloadingPoint->setName1(name1: $name);
        $unloadingPoint->setName2();
        $unloadingPoint->setStreet(street: $street);
        $unloadingPoint->setHouseNumber(houseNumber: $housnmb);
        $unloadingPoint->setPostalCode(postalCode: $plc);
        $unloadingPoint->setCity(city: $city);
        $unloadingPoint->setDistrict(district: $district);
        $unloadingPoint->setCountry(country: $country);
        $unloadingPoint->setDsdId(dsdId: 'abc');

        switch ($state) {
            case 'BB':
                $unloadingPoint->setState(state: $this->getReference(name: StateFixtures::STATE_REFERENCE_BB, class: State::class));
                break;
            case 'NW':
                $unloadingPoint->setState(state: $this->getReference(name: StateFixtures::STATE_REFERENCE_NW, class: State::class));
                break;
            case 'SA':
                $unloadingPoint->setState(state: $this->getReference(name: StateFixtures::STATE_REFERENCE_ST, class: State::class));
                break;
        }

        $this->manager->persist($unloadingPoint);
        $this->manager->flush();

        return $unloadingPoint;
    }

    public function getDependencies(): array
    {
        return [
            StateFixtures::class,
        ];
    }

    public static function getGroups(): array
    {
        return ['orderSupply', 'unloadingPoint', 'collectRegistration'];
    }
}
