<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Order;
use App\Repository\Main\CollectingPlaceRepository;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Console\Output\ConsoleOutput;

class OrderFixtures extends Fixture implements FixtureGroupInterface, DependentFixtureInterface
{
    private ?ObjectManager $manager = null;

    private readonly ConsoleOutput $out;

    /**
     * OrderFixture constructor.
     */
    public function __construct()
    {
        $this->out = new ConsoleOutput();
    }

    public function load(ObjectManager $manager): void
    {
        $this->out->writeln(messages: 'Start creating orders');

        $this->manager = $manager;

        /** @var CollectingPlaceRepository $cPlaceRepo */
        $cPlaceRepo = $this->manager->getRepository(CollectingPlace::class);
        $cPlaces = $cPlaceRepo->findAll();

        if (0 < count(value: $cPlaces)) {
        }

        $dt = new \DateTime();

        // orders for this week
        if (1 !== intval(value: $dt->format(format: 'N'))) {
            $dt->modify(modifier: 'last monday');
        }
        $this->createOrdersFor5Days(dt: $dt);

        // orders for next week
        $dt->modify(modifier: 'next monday');
        $this->createOrdersFor5Days(dt: $dt);

        $this->out->writeln(messages: 'Creating CollectRegistrations Done.');
    }

    public function createOrderRegistration(\DateTime $date, ?\DateTime $transfered = null): void
    {
        $this->out->writeln(messages: 'CreatingCollectRegistration '.$date->format(format: 'd.m.Y'));

        $order = new Order();

        $order->setDate(date: $date);
        $order->setOrderId(orderId: '123');

        $order->setSystemProvider(
            systemProvider: $this->getReference(name: SystemProviderFixtures::SYSTEMPROVIDER_REFERENCE)
        );

        $order->setCollectingPlace(
            collectingPlace: $this->getReference(name: CollectingPlaceFixtures::COLLECTINGPLACE_REFERENCE)
        );

        $order->setUnloadingPoint(
            unloadingPoint: $this->getReference(name: UnloadingPointFixtures::UNLOADINGPOINT_REFERENCE)
        );

        $order->setContractArea(
            contractArea: $this->getReference(name: ContractAreaFixtures::CONTRACT_AREA_REFERENCE)
        );

        $order->setContract(
            contract: $this->getReference(name: ContractFixtures::CONTRACT_REFERENCE)
        );

        if (!is_null(value: $transfered)) {
            $order->setTransfered(transfered: $transfered);
            $order->setTransferStatus(transferStatus: 'S');
        }
        $this->manager->persist($order);
        $this->manager->flush();
    }

    public function getDependencies(): array
    {
        return [
            SystemProviderFixtures::class,
            CollectingPlaceFixtures::class,
            ContractAreaFixtures::class,
            ContractFixtures::class,
        ];
    }

    /**
     * @return string[]
     */
    public static function getGroups(): array
    {
        return ['collectRegistration'];
    }

    private function createOrdersFor5Days(\DateTime $dt): void
    {
        // for 5 days (mon to fri)
        for ($i = 1; $i <= 5; ++$i) {
            $dayEntry = random_int(min: 1, max: 2);

            // for 1 up to 2 entries each day
            for ($d = 1; $d <= $dayEntry; ++$d) {
                $trand = random_int(min: 0, max: 4);
                if (4 === $trand) {
                    $transferred = clone $dt;
                    $transferred->modify(modifier: '+1 minute');
                } else {
                    $transferred = null;
                }

                $this->createOrderRegistration(date: $dt, transfered: $transferred);
            }

            $dt->modify(modifier: '+1 day');
        }
    }
}
