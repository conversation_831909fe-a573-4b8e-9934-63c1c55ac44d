<?php

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\Main\UnloadingPoint;
use App\Services\UnloadingPointHelper;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class UnloadingPointController.
 */
class UnloadingPointController extends AbstractFOSRestController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager, private readonly UnloadingPointHelper $unloadingPointHelper)
    {
    }

    /**
     * Create UnloadingPoint
     * Request to create a contract area, if the contractAreaId exist parameter error message is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/unloadingPoints', methods: ['POST'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['unloadingPointId', 'dsdId', 'name1', 'name2', 'street', 'houseNumber', 'postalCode', 'city', 'district', 'state', 'country'],
                properties: [
                    new OA\Property(property: 'unloadingPointId', type: 'integer', example: '321'),
                    new OA\Property(property: 'dsdId', type: 'string', example: '321'),
                    new OA\Property(property: 'name1', type: 'string', example: 'B & E Antriebselemente GmbH'),
                    new OA\Property(property: 'name2', type: 'string', example: 'Antriebe aller Art'),
                    new OA\Property(property: 'street', type: 'string', example: 'Lange Str.'),
                    new OA\Property(property: 'houseNumber', type: 'string', example: '8'),
                    new OA\Property(property: 'postalCode', type: 'string', example: '33014'),
                    new OA\Property(property: 'city', type: 'string', example: 'Bad Driburg'),
                    new OA\Property(property: 'district', type: 'string', example: 'Test'),
                    new OA\Property(property: 'state', type: 'string', example: 'BB'),
                    new OA\Property(property: 'country', type: 'string', example: 'DE'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 201, description: 'Created')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Response(response: 409, description: 'Already exist')]
    #[OA\Tag(name: 'UnloadingPoint')]
    public function addUnloadingPoints(Request $request): Response
    {
        $unloadingPointRepo = $this->manager->getRepository(UnloadingPoint::class);

        $unloadingPointId = $request->get(key: 'unloadingPointId');

        // check if unloadingPoint already exists
        $unloadingPoint = $unloadingPointRepo->findOneBy(
            criteria: ['unloadingPointId' => $unloadingPointId]
        );

        if ($unloadingPoint instanceof UnloadingPoint) {
            return $this->updateUnloadingPoints(request: $request);
            // return $this->viewUnloadingPointExist($unloadingPointId);
        }

        if (is_null(value: $request->get(key: 'dsdId'))) {
            return $this->viewDsdIdNotSet();
        }

        if (is_null(value: $request->get(key: 'state'))) {
            return $this->viewStateNotSet();
        }

        if (is_null(value: $request->get(key: 'country'))) {
            return $this->viewCountryNotSet();
        }

        // create contract area
        $unloadingPoint = $this->unloadingPointHelper->createPlace(values: [
            'unloadingPointId' => $request->get(key: 'unloadingPointId'),
            'dsdId' => $request->get(key: 'dsdId'),
            'name1' => $request->get(key: 'name1'),
            'name2' => $request->get(key: 'name2'),
            'street' => $request->get(key: 'street'),
            'houseNumber' => $request->get(key: 'houseNumber'),
            'postalCode' => $request->get(key: 'postalCode'),
            'city' => $request->get(key: 'city'),
            'district' => $request->get(key: 'district'),
            'state' => $request->get(key: 'state'),
            'country' => $request->get(key: 'country'),
        ]);

        $view = $this->view(
            data: [
                'id' => $unloadingPoint->getId(),
                'unloadingPointId' => $unloadingPoint->getUnloadingPointId(),
                'dsdId' => $unloadingPoint->getDsdId(),
                'name1' => $unloadingPoint->getName1(),
                'name2' => $unloadingPoint->getName2(),
                'street' => $unloadingPoint->getStreet(),
                'houseNumber' => $unloadingPoint->getHouseNumber(),
                'postalCode' => $unloadingPoint->getPostalCode(),
                'city' => $unloadingPoint->getCity(),
                'district' => $unloadingPoint->getDistrict(),
                'state' => $unloadingPoint->getState()->getShortName(),
                'country' => $unloadingPoint->getCountry(),
            ], statusCode: 201
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Update UnloadingPoint
     * Put Request to update a collecting place, if the collecting place id is NOT existing, error message is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/unloadingPoints/{unloadingPointId}', requirements: ['unloadingPointId' => '^[\d]+$'], methods: ['PUT'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'unloadingPointId', in: 'path', example: '321')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['unloadingPointId'],
                properties: [
                    new OA\Property(property: 'unloadingPointId', type: 'integer', example: '321'),
                    new OA\Property(property: 'dsdId', type: 'string', example: '321'),
                    new OA\Property(property: 'name1', type: 'string', example: 'B & E Antriebselemente GmbH'),
                    new OA\Property(property: 'name2', type: 'string', example: 'Antriebe aller Art'),
                    new OA\Property(property: 'street', type: 'string', example: 'Lange Str.'),
                    new OA\Property(property: 'houseNumber', type: 'string', example: '8'),
                    new OA\Property(property: 'postalCode', type: 'string', example: '33014'),
                    new OA\Property(property: 'city', type: 'string', example: 'Bad Driburg'),
                    new OA\Property(property: 'district', type: 'string', example: 'Test'),
                    new OA\Property(property: 'state', type: 'string', example: 'BB'),
                    new OA\Property(property: 'country', type: 'string', example: 'DE'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 200, description: 'Update OK')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'UnloadingPoint')]
    public function updateUnloadingPoints(Request $request): Response
    {
        $unloadingPointRepo = $this->manager->getRepository(UnloadingPoint::class);

        $unloadingPointId = $request->get(key: 'unloadingPointId');

        /** @var UnloadingPoint $unloadingPoint */
        $unloadingPoint = $unloadingPointRepo->findOneBy(
            criteria: ['unloadingPointId' => $unloadingPointId]
        );

        if (is_null(value: $unloadingPoint)) {
            return $this->viewUnloadingPointNotExist(unloadingPointId: $unloadingPointId);
        }

        if (is_null(value: $request->get(key: 'dsdId'))) {
            return $this->viewDsdIdNotSet();
        }

        if (is_null(value: $request->get(key: 'state'))) {
            return $this->viewStateNotSet();
        }

        if (is_null(value: $request->get(key: 'country'))) {
            return $this->viewCountryNotSet();
        }

        // update collecting place
        $unloadingPoint = $this->unloadingPointHelper->saveUnloadingPoint(values: [
            'unloadingPointId' => $request->get(key: 'unloadingPointId'),
            'dsdId' => $request->get(key: 'dsdId'),
            'name1' => $request->get(key: 'name1'),
            'name2' => $request->get(key: 'name2'),
            'street' => $request->get(key: 'street'),
            'houseNumber' => $request->get(key: 'houseNumber'),
            'postalCode' => $request->get(key: 'postalCode'),
            'city' => $request->get(key: 'city'),
            'district' => $request->get(key: 'district'),
            'state' => $request->get(key: 'state'),
            'country' => $request->get(key: 'country'),
        ], unloadingPoint: $unloadingPoint);

        $view = $this->view(
            data: [
                'id' => $unloadingPoint->getId(),
                'unloadingPointId' => $unloadingPoint->getUnloadingPointId(),
                'dsdId' => $unloadingPoint->getDsdId(),
                'name1' => $unloadingPoint->getName1(),
                'name2' => $unloadingPoint->getName2(),
                'street' => $unloadingPoint->getStreet(),
                'houseNumber' => $unloadingPoint->getHouseNumber(),
                'postalCode' => $unloadingPoint->getPostalCode(),
                'city' => $unloadingPoint->getCity(),
                'district' => $unloadingPoint->getDistrict(),
                'state' => $unloadingPoint->getState()->getShortName(),
                'country' => $unloadingPoint->getCountry(),
            ], statusCode: 200
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewUnloadingPointNotExist($unloadingPointId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: UnloadingPoint NOT found!',
                'contractAreaId' => $unloadingPointId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewDsdIdNotSet(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: DSD Id not set!',
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewStateNotSet(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: State not set!',
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewCountryNotSet(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Country not set!',
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }
}
