<?php

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\Main\SystemProvider;
use App\Services\SystemProviderHelper;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class SystemProviderController.
 */
class SystemProviderController extends AbstractFOSRestController
{
    /**
     * Constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager, private readonly SystemProviderHelper $systemProviderHelper)
    {
    }

    /**
     * Create SystemProvider
     * Request to create a system provider, if the systemProviderId exists an error is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/systemProviders', methods: ['POST'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(property: 'materialId', type: 'string', example: 'bea7bc08-fcab-42fb-a419-0692a769fdf2'),
                    new OA\Property(property: 'systemProviderId', type: 'string', example: '106968'),
                    new OA\Property(property: 'name', type: 'string', example: 'PZ Dual'),
                    new OA\Property(property: 'longtext', type: 'string', example: 'PreZero Dual'),
                    new OA\Property(property: 'dsdFractionId', type: 'string', example: '0600'),
                    new OA\Property(property: 'dsdFractionName', type: 'string', example: 'LVP (wie gesammelt)'),
                    new OA\Property(property: 'transport', type: 'boolean', example: 'true'),
                    new OA\Property(property: 'monoFraction', type: 'boolean', example: 'false'),
                ]
            )
        ),
    )]
    #[OA\Response(response: 201, description: 'Created')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Response(response: 409, description: 'Already exist')]
    #[OA\Tag(name: 'SystemProvider')]
    public function addSystemProvider(Request $request): Response
    {
        $systemProviderRepo = $this->manager->getRepository(SystemProvider::class);

        $request->get(key: 'materialId');
        $systemProviderId = $request->get(key: 'systemProviderId');

        /** @var SystemProvider $systemProvider */
        $systemProvider = $systemProviderRepo->findOneBy(
            // ['materialId'=>$materialId]
            criteria: ['systemProviderId' => $systemProviderId]
        );

        if ($systemProvider) {
            return $this->updateSystemProvider(request: $request);
            // return $this->viewSystemProviderExist($systemProviderId);
        }

        // create system provider
        $systemProvider = $this->systemProviderHelper->createProvider(values: [
            'materialId' => $request->get(key: 'materialId'),
            'systemProviderId' => $request->get(key: 'systemProviderId'),
            'name' => $request->get(key: 'name'),
            'longtext' => $request->get(key: 'longtext'),
            'dsdFractionId' => $request->get(key: 'dsdFractionId'),
            'dsdFractionName' => $request->get(key: 'dsdFractionName'),
            'transport' => $request->get(key: 'transport'),
            'monoFraction' => $request->get(key: 'monoFraction'),
        ]);

        $view = $this->view(
            data: [
                'materialId' => $systemProvider->getMaterialId(),
                'systemProviderId' => $systemProvider->getSystemProviderId(),
                'name' => $systemProvider->getName(),
                'longtext' => $systemProvider->getLongtext(),
                'dsdFractionId' => $systemProvider->getDsdFractionId(),
                'dsdFractionName' => $systemProvider->getdsdFractionName(),
                'transport' => $systemProvider->getTransport(),
                'monoFraction' => $systemProvider->getMonoFraction(),
            ], statusCode: 201
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Update SystemProvider
     * Put Request to update a system provider, if the system provider is NOT existing, an error message is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/systemProviders/{systemProviderId}', methods: ['PUT'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'systemProviderId', in: 'path', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['collectingPlaceId'],
                properties: [
                    new OA\Property(property: 'materialId', type: 'string', example: 'bea7bc08-fcab-42fb-a419-0692a769fdf2'),
                    new OA\Property(property: 'systemProviderId', type: 'string', example: '106968'),
                    new OA\Property(property: 'name', type: 'string', example: 'PZ Dual'),
                    new OA\Property(property: 'longtext', type: 'string', example: 'PreZero Dual'),
                    new OA\Property(property: 'dsdFractionId', type: 'string', example: '0600'),
                    new OA\Property(property: 'dsdFractionName', type: 'string', example: 'LVP (wie gesammelt)'),
                    new OA\Property(property: 'transport', type: 'boolean', example: 'true'),
                    new OA\Property(property: 'monoFraction', type: 'boolean', example: 'false'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 200, description: 'Update OK')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'SystemProvider')]
    public function updateSystemProvider(Request $request): Response
    {
        $systemProviderRepo = $this->manager->getRepository(SystemProvider::class);

        $materialId = $request->get(key: 'materialId');
        $systemProviderId = $request->get(key: 'systemProviderId');

        /** @var SystemProvider $systemProvider */
        $systemProvider = $systemProviderRepo->findOneBy(
            // ['materialId'=>$materialId]
            criteria: ['systemProviderId' => $systemProviderId]
        );

        if (is_null(value: $systemProvider)) {
            return $this->viewSystemProviderNotExist(materialId: $materialId);
        }

        // update system provider
        $this->systemProviderHelper->saveProvider(values: [
            'materialId' => $request->get(key: 'materialId'),
            'systemProviderId' => $request->get(key: 'systemProviderId'),
            'name' => $request->get(key: 'name'),
            'longtext' => $request->get(key: 'longtext'),
            'dsdFractionId' => $request->get(key: 'dsdFractionId'),
            'dsdFractionName' => $request->get(key: 'dsdFractionName'),
            'transport' => $request->get(key: 'transport'),
            'monoFraction' => $request->get(key: 'monoFraction'),
        ], systemProvider: $systemProvider);

        $view = $this->view(
            data: [
                'materialId' => $systemProvider->getMaterialId(),
                'systemProviderId' => $systemProvider->getSystemProviderId(),
                'name' => $systemProvider->getName(),
                'longtext' => $systemProvider->getLongtext(),
                'dsdFractionId' => $systemProvider->getDsdFractionId(),
                'dsdFractionName' => $systemProvider->getdsdFractionName(),
                'transport' => $systemProvider->getTransport(),
                'monoFraction' => $systemProvider->getMonoFraction(),
            ], statusCode: 200
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewSystemProviderNotExist($materialId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: SystemProvider NOT found!',
                'materialId' => $materialId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }
}
