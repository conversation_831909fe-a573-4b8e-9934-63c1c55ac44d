<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Main\User;
use App\Form\Type\PasswordNewType;
use App\Form\Type\PasswordResetType;
use App\Repository\Main\UserRepository;
use App\Services\Mailer;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;

class SecurityController extends AbstractController
{
    #[Route(path: '/login', name: 'app_login')]
    public function login(AuthenticationUtils $authenticationUtils): Response
    {
        if ($this->getUser() instanceof UserInterface) {
            return $this->redirectToRoute(route: 'app_default_orderoverview');
        }

        // get the login error if there is one
        $error = $authenticationUtils->getLastAuthenticationError();

        // last username entered by the user
        $lastUsername = $authenticationUtils->getLastUsername();

        return $this->render(view: 'security/login.html.twig', parameters: ['last_username' => $lastUsername, 'error' => $error]);
    }

    /**
     * Password Reset / Passwort zurück setzen.
     *
     * @return array|string[]|Response
     */
    #[Template('security/passwordreset.html.twig')]
    #[Route(path: '/passwordreset')]
    public function passwordreset(UserRepository $repoUser, Request $request, Mailer $mailer): array|Response
    {
        $tmpUser = new User();

        $form = $this->createForm(type: PasswordResetType::class, data: $tmpUser);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $searchMail = $tmpUser->getEmail();
            $user = $repoUser->findOneBy(criteria: ['email' => $searchMail]);

            if ($user) {
                try {
                    $mailer->sendPasswordReset(user: $user);
                } catch (\Exception $e) {
                    throw new \RuntimeException(message: 'Sorry, something went wrong during the password reset.'.$e->getMessage().' '.$e->getTraceAsString(), code: $e->getCode(), previous: $e);
                }

                return $this->render(view: 'security/passwordreset_mailsend.html.twig');
            } else {
                // return ['form'=>$form->createView(), 'errorMessage'=>'Email could not be found.'];
                return $this->render(view: 'security/passwordreset_mailsend.html.twig');
            }
        }

        return ['form' => $form->createView()];
    }

    #[Template('security/newpassword.html.twig')]
    #[Route(path: '/newpassword/{resetHash}')]
    public function newpassword(
        EntityManagerInterface $em,
        UserRepository $repoUser,
        Request $request,
        UserPasswordHasherInterface $passwordHasher,
        string $resetHash,
    ): Response|array {
        $user = $repoUser->findOneBy(criteria: ['passwordResetHash' => $resetHash]);

        if ($user) {
            $form = $this->createForm(type: PasswordNewType::class, data: []);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                $data = $form->getViewData();

                if ($data['password_1'] === $data['password_2']) {
                    // if the new password is the same as before, the next if should fail!
                    if (!$passwordHasher->isPasswordValid($user, $data['password_1'])) {
                        $user->setPasswordResetHash();
                        $user->setPassword(password: $passwordHasher->hashPassword(
                            $user,
                            $data['password_1']
                        ));
                        $user->setResetPassword(resetPassword: false);
                        $user->setPasswordDate(passwordDate: new \DateTime());
                        $em->persist($user);
                        $em->flush();

                        return $this->render(view: 'security/newpassword_ok.html.twig');
                    } else {
                        return [
                            'form' => $form->createView(),
                            'errorMessage' => 'The password is the same as before. Please type in a new one.',
                        ];
                    }
                } else {
                    return [
                        'form' => $form->createView(),
                        'errorMessage' => 'The two passwords are not equal. Please try again.',
                    ];
                }
            } else {
                return ['form' => $form->createView()];
            }
        } else {
            throw new NotFoundHttpException(message: 'No User for the link has been found.', code: 404);
        }
    }

    #[Route(path: '/logout', name: 'app_logout')]
    public function logout(): never
    {
        throw new \LogicException(message: 'This method can be blank - it will be intercepted by the logout key on your firewall.');
    }
}
