<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Entity\Main\ContractAreaValidity;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IntegerField;
use Ramsey\Uuid\Uuid;
use Symfony\Contracts\Translation\TranslatorInterface;

class ContractAreaValidityCrudController extends AbstractCrudController
{
    public function __construct(private readonly TranslatorInterface $translator)
    {
    }

    public static function getEntityFqcn(): string
    {
        return ContractAreaValidity::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            // ...
            ->add(pageName: Crud::PAGE_INDEX, actionNameOrObject: Action::DETAIL)
        ;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
        ->setSearchFields(fieldNames: ['contractArea.name'])
            ->setEntityLabelInSingular(label: $this->translator->trans('contract_area_validity'))
            ->setEntityLabelInPlural(label: $this->translator->trans('contract_areas_validity'));
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            IdField::new(propertyName: 'id')->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            AssociationField::new(propertyName: 'contractArea', label: $this->translator->trans('contact.contract_area')),
            IntegerField::new(propertyName: 'amountDay', label: $this->translator->trans('quantity_per_day')),
            IntegerField::new(propertyName: 'amountWeek', label: $this->translator->trans('quantity_per_week')),
            DateField::new(propertyName: 'valid_from', label: $this->translator->trans('contractArea.field.validFrom')),
            DateField::new(propertyName: 'valid_to', label: $this->translator->trans('contractArea.field.validTo')),
        ];
    }

    public function persistEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        $this->updateEntity(entityManager: $entityManager, entityInstance: $entityInstance);
    }

    public function updateEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        if (empty($entityInstance->getValidityId())) {
            $entityInstance->setValidityId(Uuid::uuid4()->toString());
        }

        $entityManager->persist($entityInstance);
        $entityManager->flush();
    }
}
