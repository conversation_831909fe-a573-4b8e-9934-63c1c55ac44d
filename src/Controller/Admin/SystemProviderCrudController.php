<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Entity\Main\SystemProvider;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use Symfony\Contracts\Translation\TranslatorInterface;

class SystemProviderCrudController extends AbstractCrudController
{
    public function __construct(private readonly TranslatorInterface $translator)
    {
    }

    public static function getEntityFqcn(): string
    {
        return SystemProvider::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            // ...
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::NEW)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::EDIT)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::DELETE)
        ;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular(label: $this->translator->trans('system_provider'))
            ->setEntityLabelInPlural(label: $this->translator->trans('system_provider'));
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            IdField::new(propertyName: 'id')->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'system_provider_id', label: $this->translator->trans('system_provider_id'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'name', label: $this->translator->trans('name'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
        ];
    }
}
