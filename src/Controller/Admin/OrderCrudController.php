<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Entity\Main\Order;
use App\Services\OrderHelper;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateTimeField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use Symfony\Contracts\Translation\TranslatorInterface;

class OrderCrudController extends AbstractCrudController
{
    public function __construct(private readonly OrderHelper $orderHelper, private readonly TranslatorInterface $translator)
    {
    }

    public static function getEntityFqcn(): string
    {
        return Order::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            // ...
            ->add(pageName: Crud::PAGE_INDEX, actionNameOrObject: Action::DETAIL)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::NEW)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::EDIT)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::DELETE)
            ->remove(pageName: Crud::PAGE_DETAIL, actionName: Action::DELETE)
        ;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular(label: $this->translator->trans('order'))
            ->setEntityLabelInPlural(label: $this->translator->trans('orders'));
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            IdField::new(propertyName: 'id')->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            AssociationField::new(propertyName: 'contractArea', label: $this->translator->trans('collectingplace.contract_area'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            AssociationField::new(propertyName: 'collectingPlace', label: $this->translator->trans('collectingplace')),
            AssociationField::new(propertyName: 'systemProvider', label: $this->translator->trans('system_provider'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            AssociationField::new(propertyName: 'unloadingPoint', label: $this->translator->trans('unloading_point')),
            DateField::new(propertyName: 'date', label: $this->translator->trans('order.date')),
            DateTimeField::new(propertyName: 'transfered', label: $this->translator->trans('transfer_date'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            ChoiceField::new(propertyName: 'transferStatus', label: $this->translator->trans('transfer_status'))->setChoices(choiceGenerator: ['Erfolgreich' => 'S', 'Fehler' => 'E', 'Abbruch' => 'A'])->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'driverMessage', label: $this->translator->trans('order.driver_message'))->onlyOnDetail(),
            TextField::new(propertyName: 'dispoMessage', label: $this->translator->trans('overdraft_info'))->onlyOnDetail(),
        ];
    }

    public function updateEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        if ('S' !== $entityInstance->getTransferStatus()) {
            $entityManager->persist($entityInstance);
            $entityManager->flush();

            $this->orderHelper->send(order: $entityInstance);
        }
    }
}
