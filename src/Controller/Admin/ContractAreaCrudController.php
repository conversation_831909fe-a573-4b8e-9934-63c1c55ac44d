<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Entity\Main\ContractArea;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\DateField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use Symfony\Contracts\Translation\TranslatorInterface;

class ContractAreaCrudController extends AbstractCrudController
{
    public function __construct(private readonly TranslatorInterface $translator)
    {
    }

    public static function getEntityFqcn(): string
    {
        return ContractArea::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            // ...
            ->add(pageName: Crud::PAGE_INDEX, actionNameOrObject: Action::DETAIL)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::NEW)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::EDIT)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::DELETE)
            ->remove(pageName: Crud::PAGE_DETAIL, actionName: Action::EDIT)
            ->remove(pageName: Crud::PAGE_DETAIL, actionName: Action::DELETE)
        ;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular(label: $this->translator->trans('contact.contract_area'))
            ->setEntityLabelInPlural(label: $this->translator->trans('management.contract_areas'));
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            IdField::new(propertyName: 'id')->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'name', label: $this->translator->trans('contact.contract_area'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            DateField::new(propertyName: 'valid_from', label: $this->translator->trans('contractArea.field.validFrom'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            DateField::new(propertyName: 'valid_to', label: $this->translator->trans('contractArea.field.validTo'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            ArrayField::new(propertyName: 'contractAreaValidities', label: $this->translator->trans('contractArea.field.validTo'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled')->onlyOnDetail(),
        ];
    }
}
